document.addEventListener('DOMContentLoaded', function() {
    const testBtn = document.getElementById('testBtn');
  const aiOrganizeBtn = document.getElementById('aiOrganizeBtn');
    const simpleOrganizeBtn = document.getElementById('simpleOrganizeBtn');
    const exportBtn = document.getElementById('exportBtn');
    const saveConfigBtn = document.getElementById('saveConfig');
    const status = document.getElementById('status');
    const apiKeyInput = document.getElementById('apiKey');
    const modelSelect = document.getElementById('model');
  
    // Load saved configuration
    chrome.storage.sync.get(['apiKey', 'model'], function(result) {
      if (result.apiKey) {
        apiKeyInput.value = result.apiKey;
      }
      if (result.model) {
        modelSelect.value = result.model;
      }
    });
  
    saveConfigBtn.addEventListener('click', function() {
      const apiKey = apiKeyInput.value.trim();
      if (!apiKey) {
        showStatus('Please enter your OpenAI API key', 'error');
        return;
      }
      
      chrome.storage.sync.set({
        apiKey: apiKey,
        model: modelSelect.value
      }, function() {
        showStatus('Configuration saved!', 'success');
      });
    });

    testBtn.addEventListener('click', function() {
      console.log('🔧 Test button clicked');
      showStatus('🔧 Running basic tests...', 'info');

      // Test 1: Chrome APIs
      console.log('Testing Chrome APIs...');
      if (typeof chrome !== 'undefined' && chrome.tabs && chrome.windows && chrome.storage) {
        console.log('✅ Chrome APIs available');
        showStatus('✅ Chrome APIs: OK', 'success');
      } else {
        console.log('❌ Chrome APIs missing');
        showStatus('❌ Chrome APIs: MISSING', 'error');
        return;
      }

      // Test 2: Get tabs
      chrome.tabs.query({}, function(tabs) {
        console.log(`✅ Found ${tabs.length} tabs`);
        showStatus(`✅ Found ${tabs.length} tabs`, 'success');

        // Test 3: Check storage
        chrome.storage.sync.get(['apiKey', 'model'], function(result) {
          console.log('Storage result:', result);
          if (result.apiKey) {
            console.log('✅ API key configured');
            showStatus('✅ All tests passed! Try AI Smart Organize now.', 'success');
          } else {
            console.log('⚠️ No API key configured');
            showStatus('⚠️ Tests passed but no API key configured', 'info');
          }
        });
      });
    });

    aiOrganizeBtn.addEventListener('click', function() {
      console.log('🔵 AI Organize button clicked');
      showStatus('🔄 Starting AI organization...', 'info');

      chrome.storage.sync.get(['apiKey', 'model'], function(result) {
        console.log('🔵 Retrieved config:', { hasApiKey: !!result.apiKey, model: result.model });

        if (!result.apiKey) {
          console.log('❌ No API key found');
          showStatus('Please configure your OpenAI API key first', 'error');
          return;
        }

        console.log('🔵 Querying tabs...');
        chrome.tabs.query({}, function(tabs) {
          console.log('🔵 Found tabs:', tabs.length);

          if (tabs.length === 0) {
            console.log('❌ No tabs found');
            showStatus('No tabs found to organize', 'error');
            return;
          }

          console.log('🔵 Calling aiOrganizeTabs...');
          aiOrganizeTabs(tabs, result.apiKey, result.model).catch(error => {
            console.error('❌ Error in aiOrganizeTabs:', error);
            showStatus(`Error: ${error.message}`, 'error');
          });
        });
      });
    });
  
    simpleOrganizeBtn.addEventListener('click', function() {
      chrome.tabs.query({}, function(tabs) {
        simpleOrganizeTabs(tabs);
      });
    });
  
    exportBtn.addEventListener('click', function() {
      chrome.tabs.query({}, function(tabs) {
        exportUrls(tabs);
      });
    });
  
    async function aiOrganizeTabs(tabs, apiKey, model) {
      console.log('aiOrganizeTabs called with:', { tabCount: tabs.length, hasApiKey: !!apiKey, model });
      showStatus('🤖 AI is analyzing your tabs...', 'info');

      try {
        // Remove duplicates
        const uniqueTabs = removeDuplicates(tabs);
        console.log('Unique tabs:', uniqueTabs.length);
        showStatus(`Found ${uniqueTabs.length} unique tabs. Getting AI suggestions...`, 'info');

        // Prepare data for AI analysis
        const tabData = uniqueTabs.map(tab => ({
          title: tab.title,
          url: tab.url,
          domain: getDomain(tab.url)
        }));

        console.log('Tab data prepared:', tabData);

        // Get AI categorization with timeout
        console.log('🔵 Calling AI categorization...');
        const categories = await Promise.race([
          getAiCategorization(tabData, apiKey, model),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('AI request timed out after 30 seconds')), 30000)
          )
        ]);

        console.log('AI categorization result:', categories);

        if (categories.error) {
          console.log('❌ AI categorization failed:', categories.error);
          showStatus(`AI Error: ${categories.error}. Falling back to simple organization...`, 'error');
          // Fallback to simple organization
          setTimeout(() => simpleOrganizeTabs(uniqueTabs), 2000);
          return;
        }

        // Create windows based on AI suggestions
        await createWindowsFromCategories(categories, uniqueTabs);
        showStatus(`✅ Created ${Object.keys(categories).length} organized windows!`, 'success');

      } catch (error) {
        console.error('Error in aiOrganizeTabs:', error);
        showStatus(`Error: ${error.message}`, 'error');
      }
    }
  
    async function getAiCategorization(tabData, apiKey, model) {
      console.log('getAiCategorization called with:', { tabDataLength: tabData.length, model });
      const prompt = `Analyze these browser tabs and organize them into logical, meaningful categories.
      Create category names that are professional and descriptive. Aim for 3-8 categories maximum.

      Tabs to categorize:
      ${tabData.map((tab, i) => `${i + 1}. ${tab.title} (${tab.domain})`).join('\n')}

      IMPORTANT: Respond with ONLY a valid JSON object. Do not include any markdown formatting, code blocks, or explanatory text.
      The JSON should have category names as keys and arrays of tab indices (1-based) as values.

      Example format: {"Work & Productivity": [1, 3, 5], "Social Media": [2, 4], "Learning": [6, 7, 8]}`;

      console.log('Sending prompt to OpenAI:', prompt);

      try {
        console.log('Making API request to OpenAI...');
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: model || 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are a helpful assistant that organizes browser tabs into logical categories. Always respond with valid JSON only.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            max_tokens: 1000,
            temperature: 0.3
          })
        });
  
        console.log('API response status:', response.status);
        console.log('API response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch (e) {
            const errorText = await response.text();
            console.error('API error response (raw text):', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText || 'Unknown error'}`);
          }
          console.error('API error response:', errorData);
          throw new Error(errorData.error?.message || `HTTP ${response.status}`);
        }

        let data;
        try {
          data = await response.json();
        } catch (e) {
          const responseText = await response.text();
          console.error('Failed to parse response as JSON. Raw response:', responseText);
          throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}...`);
        }

        console.log('API response data:', data);

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
          console.error('Unexpected API response structure:', data);
          throw new Error('Invalid API response structure');
        }

        let content = data.choices[0].message.content.trim();
        console.log('AI response content:', content);

        // Check if content is empty or invalid
        if (!content) {
          throw new Error('Empty response from AI');
        }

        // Remove markdown code blocks if present
        if (content.startsWith('```json')) {
          content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (content.startsWith('```')) {
          content = content.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        console.log('Cleaned content for parsing:', content);
        console.log('Content length:', content.length);

        // Validate content looks like JSON
        if (!content.startsWith('{') || !content.endsWith('}')) {
          console.error('Content does not look like JSON:', content);
          throw new Error(`AI returned non-JSON content: ${content.substring(0, 100)}...`);
        }

        // Parse JSON response
        let categories;
        try {
          categories = JSON.parse(content);
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          console.error('Content that failed to parse:', content);
          throw new Error(`Failed to parse AI response as JSON: ${parseError.message}`);
        }

        console.log('Parsed categories:', categories);

        // Convert 1-based indices to 0-based and validate
        const validatedCategories = {};
        for (const [categoryName, indices] of Object.entries(categories)) {
          validatedCategories[categoryName] = indices
            .map(i => i - 1)
            .filter(i => i >= 0 && i < tabData.length);
        }

        console.log('Validated categories:', validatedCategories);
        return validatedCategories;
        
      } catch (error) {
        console.error('AI API Error:', error);
        return { error: error.message };
      }
    }
  
    async function createWindowsFromCategories(categories, tabs) {
      console.log('createWindowsFromCategories called with:', { categories, tabCount: tabs.length });
      let isFirstWindow = true;

      for (const [categoryName, tabIndices] of Object.entries(categories)) {
        console.log(`Processing category: ${categoryName}, indices:`, tabIndices);
        if (tabIndices.length === 0) continue;

        const tabsForCategory = tabIndices.map(i => tabs[i]).filter(Boolean);
        console.log(`Tabs for category ${categoryName}:`, tabsForCategory.length);

        if (tabsForCategory.length > 0) {
          console.log(`Creating window for category: ${categoryName}`);
          // Create new window with first tab
          const window = await chrome.windows.create({
            url: tabsForCategory[0].url,
            focused: isFirstWindow
          });
          
          // Add remaining tabs to the window
          for (let i = 1; i < tabsForCategory.length; i++) {
            await chrome.tabs.create({
              windowId: window.id,
              url: tabsForCategory[i].url,
              active: false
            });
          }
          
          // Add a tab with category name as title (optional)
          await chrome.tabs.create({
            windowId: window.id,
            url: `data:text/html,<html><head><title>${categoryName}</title></head><body><h1>📁 ${categoryName}</h1><p>This window contains tabs organized by AI.</p></body></html>`,
            active: false,
            index: 0
          });
          
          isFirstWindow = false;
          
          // Small delay to avoid overwhelming the browser
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }
  
    function simpleOrganizeTabs(tabs) {
      showStatus('📁 Organizing by domain...', 'info');
      
      const uniqueTabs = removeDuplicates(tabs);
      const domainGroups = {};
      
      uniqueTabs.forEach(tab => {
        const domain = getDomain(tab.url);
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(tab);
      });
  
      // Create new windows for each domain group
      let windowsCreated = 0;
      Object.keys(domainGroups).forEach((domain, index) => {
        const tabsForDomain = domainGroups[domain];
        if (tabsForDomain.length > 1) {
          chrome.windows.create({
            url: tabsForDomain[0].url,
            focused: index === 0
          }, function(window) {
            for (let i = 1; i < tabsForDomain.length; i++) {
              chrome.tabs.create({
                windowId: window.id,
                url: tabsForDomain[i].url,
                active: false
              });
            }
          });
          windowsCreated++;
        }
      });
  
      showStatus(`✅ Created ${windowsCreated} domain-based windows`, 'success');
    }
  
    function exportUrls(tabs) {
      const uniqueTabs = removeDuplicates(tabs);
      const urlList = uniqueTabs.map(tab => `${tab.title}\n${tab.url}`).join('\n\n');
      
      const blob = new Blob([urlList], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      chrome.downloads.download({
        url: url,
        filename: `chrome-tabs-export-${new Date().toISOString().split('T')[0]}.txt`
      });
      
      showStatus(`💾 Exported ${uniqueTabs.length} unique tabs`, 'success');
    }
  
    function removeDuplicates(tabs) {
      const seen = new Set();
      return tabs.filter(tab => {
        if (seen.has(tab.url)) {
          return false;
        }
        seen.add(tab.url);
        return true;
      });
    }
  
    function getDomain(url) {
      try {
        const hostname = new URL(url).hostname;
        return hostname.replace(/^www\./, '');
      } catch {
        return 'other';
      }
    }
  
    function showStatus(message, type) {
      status.textContent = message;
      status.className = type;
      
      if (type === 'success') {
        setTimeout(() => {
          status.textContent = '';
          status.className = '';
        }, 3000);
      }
    }
  });
  