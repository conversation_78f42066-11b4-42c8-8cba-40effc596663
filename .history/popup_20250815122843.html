<!DOCTYPE html>
<html>
<head>
  <style>
    body { 
      width: 350px; 
      padding: 20px; 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    button { 
      width: 100%; 
      padding: 12px; 
      margin: 8px 0; 
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
    }
    .primary { background: #1a73e8; color: white; }
    .secondary { background: #f1f3f4; color: #202124; }
    .primary:hover { background: #1557b0; }
    .secondary:hover { background: #e8eaed; }
    
    .config-section {
      margin: 15px 0;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
    }
    
    input[type="password"], select {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #dadce0;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    label {
      font-size: 12px;
      color: #5f6368;
      font-weight: 500;
    }
    
    #status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      font-size: 13px;
    }
    
    .success { background: #e8f5e8; color: #1e8e3e; }
    .error { background: #fce8e6; color: #d93025; }
    .info { background: #e3f2fd; color: #1565c0; }
  </style>
</head>
<body>
  <h3>🤖 AI Tab Organizer</h3>
  
  <div class="config-section">
    <label for="apiKey">OpenAI API Key:</label>
    <input type="password" id="apiKey" placeholder="sk-...">
    
    <label for="model">Model:</label>
    <select id="model">
      <option value="gpt-4-turbo-preview">GPT-4 Turbo</option>
      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
    </select>
    
    <button id="saveConfig" class="secondary">Save Configuration</button>
  </div>
  
  <button id="aiOrganizeBtn" class="primary">🧠 AI Smart Organize</button>
  <button id="simpleOrganizeBtn" class="secondary">📁 Simple Domain Group</button>
  <button id="exportBtn" class="secondary">💾 Export URLs</button>
  
  <div id="status"></div>
  <script src="popup.js"></script>
</body>
</html>